import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Co<PERSON>, Check, MoreVertical, ThumbsUp, ThumbsDown, FileText, Volume2, Share2 } from 'lucide-react';
import { Message } from '@/types';
import { Button } from '@/components/ui/button';
import { cn, formatTime, copyToClipboard, formatFileSize } from '@/lib/utils';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from '@/components/ui/dropdown-menu';

interface MessageBubbleProps {
  message: Message;
  isGrouped?: boolean;
  showAvatar?: boolean;
  previousUserMessage?: Message | null;
}

const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isGrouped = false,
  showAvatar = true,
  previousUserMessage = null
}) => {
  const [copied, setCopied] = useState(false);
  const [feedback, setFeedback] = useState<'up' | 'down' | null>(null);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const isUser = message.isUser || message.sender === 'user';

  const handleCopy = async () => {
    try {
      await copyToClipboard(message.content);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy message:', error);
    }
  };

  const handleFeedback = (type: 'up' | 'down') => {
    setFeedback(feedback === type ? null : type);
    // TODO: Send feedback to backend
  };

  // Speak Out (Text-to-Speech)
  const handleSpeak = () => {
    if (!('speechSynthesis' in window)) {
      alert('Text-to-speech is not supported in this browser.');
      return;
    }
    const utterance = new window.SpeechSynthesisUtterance(message.content.replace(/<[^>]+>/g, ''));
    setIsSpeaking(true);
    utterance.onend = () => setIsSpeaking(false);
    utterance.onerror = () => setIsSpeaking(false);
    window.speechSynthesis.cancel(); // Stop any previous speech
    window.speechSynthesis.speak(utterance);
  };

  // Share Message (with dropdown)
  const shareContent = () => {
    const question = previousUserMessage ? previousUserMessage.content.replace(/<[^>]+>/g, '') : '';
    const answer = message.content.replace(/<[^>]+>/g, '');
    return question ? `Q: ${question}\nA: ${answer}` : answer;
  };

  const handleNativeShare = async () => {
    const text = shareContent();
    if (navigator.share) {
      try {
        await navigator.share({ text });
      } catch (e) {}
    } else {
      await copyToClipboard(text);
      alert('Message copied to clipboard!');
    }
  };

  const handleSlackShare = () => {
    const text = encodeURIComponent(shareContent());
    window.open(`https://slack.com/app_redirect?channel=&message=${text}`,'_blank');
  };
  const handleEmailShare = () => {
    const text = encodeURIComponent(shareContent());
    window.open(`mailto:?subject=Chatbot Q&A&body=${text}`,'_blank');
  };
  const handleLinkedInShare = () => {
    const text = encodeURIComponent(shareContent());
    window.open(`https://www.linkedin.com/sharing/share-offsite/?url=&summary=${text}`,'_blank');
  };
  const handleWhatsAppShare = () => {
    const text = encodeURIComponent(shareContent());
    window.open(`https://wa.me/?text=${text}`,'_blank');
  };
  const handleTelegramShare = () => {
    const text = encodeURIComponent(shareContent());
    window.open(`https://t.me/share/url?url=&text=${text}`,'_blank');
  };

  const renderMessageContent = (content: string) => {
    const lines = content.split('\n');
    return lines.map((line, index) => (
      <div key={index} className="mb-1 last:mb-0">
        {line}
      </div>
    ));
  };

  const renderFileAttachments = () => {
    if (!message.files || message.files.length === 0) return null;

    return (
      <div className="mt-3 space-y-2">
        {message.files.map((file) => (
          <div key={file.id} className="flex items-center gap-2 rounded-lg bg-muted/50 p-2">
            <FileText className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">{file.name}</span>
            <span className="text-xs text-muted-foreground">
              {formatFileSize(file.size)}
            </span>
          </div>
        ))}
      </div>
    );
  };

  const messageVariants = {
    initial: { opacity: 0, y: 20, scale: 0.95 },
    animate: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 500,
        damping: 30
      }
    },
    exit: { opacity: 0, y: -20, scale: 0.95 }
  };

  return (
    <motion.div
      variants={messageVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      className={cn(
        isUser
          ? "group flex gap-3 px-4 py-3 hover:bg-muted/30 transition-colors duration-200 flex-row-reverse"
          : "group flex gap-3 px-4 py-3 flex-row",
        !isGrouped && "mt-6"
      )}
    >
      {/* Avatar */}
      {/* Removed avatar icons for both user and bot for a cleaner look */}
      {/* Spacer for grouped messages */}
      {isGrouped && showAvatar && (
        <div className="w-8 shrink-0" />
      )}

      {/* Message Content */}
      <div className={cn(
        "flex flex-col gap-1 min-w-0 flex-1",
        isUser ? "items-end" : "items-start"
      )}>
        {/* Message Bubble */}
        <div className={cn(
          "chat-bubble relative group/bubble",
          isUser
            ? "chat-bubble-user bg-gray-200 text-gray-900 max-w-[700px] border border-gray-300 dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600 rounded-2xl px-6 py-4 shadow-md"
            : "chat-bubble-assistant max-w-[900px] text-gray-900 dark:text-gray-100 px-0 py-0 bg-transparent border-none shadow-none rounded-none hover:bg-transparent",
        )}>
          <div className="prose prose-base max-w-none dark:prose-invert">
            {isUser
              ? renderMessageContent(message.content)
              : <span dangerouslySetInnerHTML={{ __html: message.content }} />}
          </div>

          {/* File Attachments */}
          {renderFileAttachments()}

          {/* Message Actions - moved to bottom, horizontal row like ChatGPT */}
          {!isUser && (
            <div className="flex flex-row gap-4 mt-5 items-center justify-start">
              <Button
                variant="ghost"
                size="icon-lg"
                onClick={handleCopy}
                className="p-0 m-0 bg-transparent border-none shadow-none hover:bg-transparent focus:bg-transparent rounded-none text-[#555] hover:text-[#222] transition-colors duration-150"
                title="Copy"
              >
                {copied ? (
                  <Check className="h-4 w-4" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </Button>
              <Button
                variant="ghost"
                size="icon-lg"
                onClick={handleSpeak}
                className={`p-0 m-0 bg-transparent border-none shadow-none hover:bg-transparent focus:bg-transparent rounded-none text-[#555] hover:text-[#222] transition-colors duration-150 ${isSpeaking ? 'animate-pulse' : ''}`}
                title="Speak Out"
                aria-pressed={isSpeaking}
              >
                <Volume2 className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon-lg"
                onClick={handleNativeShare}
                className="p-0 m-0 bg-transparent border-none shadow-none hover:bg-transparent focus:bg-transparent rounded-none text-[#555] hover:text-[#222] transition-colors duration-150"
                title="Share (Native/OS)"
              >
                <Share2 className="h-4 w-4" />
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon-lg"
                    className="p-0 m-0 bg-transparent border-none shadow-none hover:bg-transparent focus:bg-transparent rounded-none text-[#555] hover:text-[#222] transition-colors duration-150"
                    title="More options"
                  >
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={handleSlackShare}>
                    <img src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons/icons/slack.svg" alt="Slack" width={16} height={16} style={{ marginRight: 10 }} />
                    Share to Slack
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleEmailShare}>
                    <img src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons/icons/microsoftoutlook.svg" alt="Outlook" width={16} height={16} style={{ marginRight: 10 }} />
                    Share via Outlook
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleEmailShare}>
                    <img src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons/icons/maildotru.svg" alt="Mail" width={16} height={16} style={{ marginRight: 10 }} />
                    Share via Mail
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleLinkedInShare}>
                    <img src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons/icons/linkedin.svg" alt="LinkedIn" width={16} height={16} style={{ marginRight: 10 }} />
                    Share to LinkedIn
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleWhatsAppShare}>
                    <img src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons/icons/whatsapp.svg" alt="WhatsApp" width={16} height={16} style={{ marginRight: 10 }} />
                    Share to WhatsApp
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleTelegramShare}>
                    <img src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons/icons/telegram.svg" alt="Telegram" width={16} height={16} style={{ marginRight: 10 }} />
                    Share to Telegram
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <Button
                variant="ghost"
                size="icon-lg"
                onClick={() => handleFeedback('up')}
                className={cn(
                  "p-0 m-0 bg-transparent border-none shadow-none hover:bg-transparent focus:bg-transparent rounded-none text-[#555] hover:text-[#222] transition-colors duration-150",
                  feedback === 'up' && "text-success-500"
                )}
                title="Like"
              >
                <ThumbsUp className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon-lg"
                onClick={() => handleFeedback('down')}
                className={cn(
                  "p-0 m-0 bg-transparent border-none shadow-none hover:bg-transparent focus:bg-transparent rounded-none text-[#555] hover:text-[#222] transition-colors duration-150",
                  feedback === 'down' && "text-destructive"
                )}
                title="Dislike"
              >
                <ThumbsDown className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>

        {/* Message Footer */}
        {/* Remove old message footer actions, keep only timestamp if needed */}
        <div className={cn(
          "flex items-center gap-2 px-1 mt-2",
          isUser ? "flex-row-reverse" : "flex-row"
        )}>
          {/* Timestamp */}
          <span className="text-xs text-muted-foreground">
            {formatTime(new Date(message.timestamp))}
          </span>
        </div>
      </div>
    </motion.div>
  );
};

export default MessageBubble;
